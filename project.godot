; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="RogueMine"
run/main_scene="res://scenes/MainMenu.tscn"
config/features=PackedStringArray("4.3", "Mobile")
boot_splash/show_image=false
boot_splash/fullsize=false
boot_splash/use_filter=false
config/icon="res://icon.svg"

[display]

window/size/viewport_width=540
window/size/viewport_height=960
window/stretch/mode="canvas_items"
window/handheld/orientation=1

[dotnet]

project/assembly_name="RogueMine"

[input_devices]

pointing/emulate_touch_from_mouse=true

[layer_names]

2d_physics/layer_1="Player"
2d_physics/layer_2="Enemies"
2d_physics/layer_3="Walls"
2d_physics/layer_4="Items"

[rendering]

textures/canvas_textures/default_texture_filter=2
renderer/rendering_method="mobile"
