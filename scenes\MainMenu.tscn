[gd_scene load_steps=3 format=3 uid="uid://c8fy8j0xk3q1g"]

[ext_resource type="Script" path="res://scripts/MainMenu.gd" id="1_2h3k4"]

[sub_resource type="LabelSettings" id="LabelSettings_1k2l3"]
font_size = 48
font_color = Color(1, 1, 1, 1)

[node name="MainMenu" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_2h3k4")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.2, 1)

[node name="VBoxContainer" type="VBoxContainer" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -300.0
offset_right = 200.0
offset_bottom = 300.0

[node name="Title" type="Label" parent="VBoxContainer"]
layout_mode = 2
text = "RogueMine"
label_settings = SubResource("LabelSettings_1k2l3")
horizontal_alignment = 1

[node name="Spacer1" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 100)

[node name="PlayButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)
text = "PLAY"

[node name="Spacer2" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="OptionsButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)
text = "OPTIONS"

[node name="Spacer3" type="Control" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 20)

[node name="QuitButton" type="Button" parent="VBoxContainer"]
layout_mode = 2
custom_minimum_size = Vector2(0, 80)
text = "QUIT"

[node name="VersionLabel" type="Label" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -100.0
offset_top = -40.0
text = "v1.0.0"
horizontal_alignment = 2

[connection signal="pressed" from="VBoxContainer/PlayButton" to="." method="_on_play_button_pressed"]
[connection signal="pressed" from="VBoxContainer/OptionsButton" to="." method="_on_options_button_pressed"]
[connection signal="pressed" from="VBoxContainer/QuitButton" to="." method="_on_quit_button_pressed"]
