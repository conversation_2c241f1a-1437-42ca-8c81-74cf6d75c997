extends Control

# Test script to verify minesweeper sprites are working correctly

const SPRITE_SIZE = 16
const SPRITES_PER_ROW = 4

var minesweeper_texture

func _ready():
	# Load the texture
	minesweeper_texture = load("res://assets/minesweeper.png")
	if minesweeper_texture:
		print("Texture loaded: ", minesweeper_texture.get_size())
	else:
		print("Failed to load texture!")
		return
	
	# Create a grid showing all sprites
	var grid_container = GridContainer.new()
	grid_container.columns = 4
	grid_container.position = Vector2(50, 50)
	add_child(grid_container)
	
	# Add all 12 sprites
	for i in range(12):
		var texture_rect = TextureRect.new()
		texture_rect.texture = minesweeper_texture
		texture_rect.region_enabled = true
		texture_rect.region_rect = _get_sprite_region(i)
		texture_rect.size = Vector2(32, 32)  # Scale up for visibility
		texture_rect.stretch_mode = TextureRect.STRETCH_KEEP
		
		# Add label
		var label = Label.new()
		label.text = str(i)
		label.position = Vector2(0, 35)
		
		var container = Control.new()
		container.size = Vector2(40, 50)
		container.add_child(texture_rect)
		container.add_child(label)
		
		grid_container.add_child(container)

func _get_sprite_region(sprite_index: int) -> Rect2:
	var col = sprite_index % SPRITES_PER_ROW
	var row = int(sprite_index / SPRITES_PER_ROW)
	return Rect2(col * SPRITE_SIZE, row * SPRITE_SIZE, SPRITE_SIZE, SPRITE_SIZE)
