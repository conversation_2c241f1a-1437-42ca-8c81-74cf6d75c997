[gd_scene load_steps=2 format=3 uid="uid://b4n5m6k7l8p9q"]

[ext_resource type="Script" path="res://scripts/GameScene.gd" id="1_a2b3c"]

[node name="GameScene" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_a2b3c")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.2, 0.3, 0.1, 1)

[node name="UI" type="CanvasLayer" parent="."]

[node name="TopPanel" type="Panel" parent="UI"]
anchors_preset = 10
anchor_right = 1.0
offset_bottom = 100.0

[node name="BackButton" type="Button" parent="UI/TopPanel"]
layout_mode = 1
anchors_preset = 4
anchor_top = 0.5
anchor_bottom = 0.5
offset_left = 20.0
offset_top = -25.0
offset_right = 120.0
offset_bottom = 25.0
text = "← BACK"

[node name="ScoreLabel" type="Label" parent="UI/TopPanel"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -50.0
offset_top = -15.0
offset_right = 50.0
offset_bottom = 15.0
text = "Score: 0"
horizontal_alignment = 1

[node name="GameArea" type="Control" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_top = 100.0

[node name="CenterLabel" type="Label" parent="GameArea"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -150.0
offset_top = -50.0
offset_right = 150.0
offset_bottom = 50.0
text = "Game Area
Tap to interact!"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="pressed" from="UI/TopPanel/BackButton" to="." method="_on_back_button_pressed"]
[connection signal="gui_input" from="GameArea" to="." method="_on_game_area_gui_input"]
